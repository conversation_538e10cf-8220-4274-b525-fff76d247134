#!/usr/bin/env python3
"""
验证测试文件是否正确的简单脚本
"""

import sys
import os

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

print("验证测试文件...")

# 检查测试文件是否存在
test_file_path = "dev_v1/Quality_Control/tests/run_tests.py"
if not os.path.exists(test_file_path):
    print(f"✗ 测试文件不存在: {test_file_path}")
    sys.exit(1)

print(f"✓ 测试文件存在: {test_file_path}")

# 检查测试数据文件是否存在
data_file_path = "dev_v1/data/discharge_summary_test_data.json"
if not os.path.exists(data_file_path):
    print(f"✗ 测试数据文件不存在: {data_file_path}")
    sys.exit(1)

print(f"✓ 测试数据文件存在: {data_file_path}")

# 尝试导入测试模块
try:
    sys.path.append("dev_v1/Quality_Control/tests")
    import run_tests
    print("✓ 测试模块导入成功")
except ImportError as e:
    print(f"✗ 测试模块导入失败: {e}")
    sys.exit(1)

# 检查测试函数是否存在
if hasattr(run_tests, 'test_discharge_summary_quality_control'):
    print("✓ 测试函数存在")
else:
    print("✗ 测试函数不存在")
    sys.exit(1)

if hasattr(run_tests, 'load_test_data'):
    print("✓ 数据加载函数存在")
else:
    print("✗ 数据加载函数不存在")
    sys.exit(1)

print("✓ 所有验证通过！测试文件创建成功")
print("\n可以运行以下命令执行测试:")
print("cd dev_v1/Quality_Control/tests && python run_tests.py")
